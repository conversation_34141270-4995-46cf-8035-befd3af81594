﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.Access.Queries
{
    public class GetRoleBusinessPermissionByRoleIdQuery : QueryBase<RoleBusinessPermissionData>
    {
        public GetRoleBusinessPermissionByRoleIdQuery(Guid roleId)
        {
            RoleId = roleId;
        }

        public Guid RoleId { get; private set; }
    }

    internal class GetRoleBusinessPermissionByRoleIdQueryHandler :
        QueryHandlerBase<GetRoleBusinessPermissionByRoleIdQuery, RoleBusinessPermissionData>
     {
    public GetRoleBusinessPermissionByRoleIdQueryHandler(
        IEntitySet entitySet,
        IRepository repository,
        IText text,
        IMapper mapper)
      : base(entitySet, repository, text, mapper)
    { }
        public override async Task<QueryResult<RoleBusinessPermissionData>> ExecuteAsync(GetRoleBusinessPermissionByRoleIdQuery query)
        {
            var rolePermissionEntities =
                await EntitySet.GetAsync<RoleBusinessPermissionEntity>().Where(x => x.RoleId == query.RoleId);
            return QueryResult.Create(rolePermissionEntities, x => Mapper.Map<RoleBusinessPermissionData>(x)));
        }
    }
}

