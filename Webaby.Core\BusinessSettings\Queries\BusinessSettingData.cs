﻿using System;
using System.Collections.Generic;
using Webaby.Core.DynamicForm;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.BusinessSettings.Queries
{
    public class BusinessSettingData : IDynamicProperty
    {
        public Guid Id { get; set; }

        public Guid ObjectId { get; set; }

        public Guid FormId { get; set; }

        public Guid FieldId { get; set; }

        public Guid? DynamicDefinedTableSchemaId { get; set; }

        public String Name { get; set; }

        public String DisplayName { get; set; }

        public string DataType { get; set; }

        public string AdditionalFilter { get; set; }

        public Type Type { get; set; }

        public String ImportKey { get; set; }

        public String Value { get; set; }

        public string ViewHint { get; set; }

        public bool Display { get; set; }

        public bool FreezeValue { get; set; }

        public bool IsExportExcel { get; set; }

        public bool IsExportByConditionBoolean { get; set; }

        public string SelectOptions { get; set; }

        public Int32 Order { get; set; }

        public bool IsReadOnly { get; set; }

        public bool IsRequired { get; set; }

        public Guid? DynamicFieldSectionId { get; set; }

        public string DynamicFieldSectionName { get; set; }

        public string InputFieldGroup { get; set; }

        public string RequiredDependency { get; set; }

        public string Inject { get; set; }

        public string Color { get; set; }

        public string BackgroundColor { get; set; }
        public FieldType FieldType { get; set; }

        public Guid? RepresentationDynamicFieldId { get; set; }

        public string VersionCode { get; set; }

        public string CustomDependencies { get; set; }

        public Dictionary<string, string> GetAdditionalMetadata()
        {
            return new Dictionary<string, string>();
        }
    }
}

