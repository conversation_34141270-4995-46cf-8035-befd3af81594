﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.Access.Queries
{
    public class AccessBusinessPermissionData
    {
        public Guid Id { get; set; }

        public Guid AccessId { get; set; }

        public Guid BusinessPermissionId { get; set; }

        public string ActionName { get; set; }

        public string ControllerName { get; set; }

        public int BusinessPermissionOrder { get; set; }

    }
}

