﻿using System.Web.Mvc;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.Access.Queries
{
    public class GetApiAccessQuery : QueryBase<AccessData>
    {
        public string UrlPart { get; set; }

        public HttpVerbs Method { get; set; }
    }

    internal class GetApiAccessQueryHandler : QueryHandlerBase<GetApiAccessQuery, AccessData>
     {
    public GetApiAccessQueryHandler(
        IEntitySet entitySet,
        IRepository repository,
        IText text,
        IMapper mapper)
      : base(entitySet, repository, text, mapper)
    { }
        public override async Task<QueryResult<AccessData>> ExecuteAsync(GetApiAccessQuery query)
        {
            var entity = from access in await EntitySet.GetAsync<AccessEntity>()
                         where access.ActionName == query.UrlPart && access.Method == query.Method
                         select access;
            return QueryResult.Create(entity, x => Mapper.Map<AccessData>(x)));
        }
    }
}

